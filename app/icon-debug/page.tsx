"use client";

import React, { useEffect, useState } from 'react';

export default function IconDebugPage() {
  const [fontLoaded, setFontLoaded] = useState(false);
  const [cdnLoaded, setCdnLoaded] = useState(false);
  const [localFontLoaded, setLocalFontLoaded] = useState(false);

  useEffect(() => {
    // Check if RemixIcon font is loaded
    const checkFontLoaded = () => {
      if (document.fonts && document.fonts.check) {
        const isLoaded = document.fonts.check('16px remixicon');
        setFontLoaded(isLoaded);
      }
    };

    // Check if CDN CSS is loaded
    const checkCdnLoaded = () => {
      const stylesheets = Array.from(document.styleSheets);
      const cdnStylesheet = stylesheets.find(sheet => 
        sheet.href && sheet.href.includes('remixicon')
      );
      setCdnLoaded(!!cdnStylesheet);
    };

    // Check if local font files exist
    const checkLocalFont = async () => {
      try {
        const response = await fetch('/assets/icon-fonts/RemixIcons/fonts/remixicon.woff2');
        setLocalFontLoaded(response.ok);
      } catch (error) {
        setLocalFontLoaded(false);
      }
    };

    checkFontLoaded();
    checkCdnLoaded();
    checkLocalFont();

    // Recheck after fonts load
    if (document.fonts) {
      document.fonts.ready.then(() => {
        checkFontLoaded();
      });
    }
  }, []);

  const testIcons = [
    { name: 'Home', class: 'ri-home-line' },
    { name: 'User', class: 'ri-user-line' },
    { name: 'Settings', class: 'ri-settings-line' },
    { name: 'Search', class: 'ri-search-line' },
    { name: 'Heart', class: 'ri-heart-line' },
  ];

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">RemixIcon Debug Page</h1>
      
      {/* Font Loading Status */}
      <div className="mb-8 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Font Loading Status</h2>
        <div className="space-y-2">
          <div className={`p-2 rounded ${fontLoaded ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            RemixIcon Font Loaded: {fontLoaded ? '✅ Yes' : '❌ No'}
          </div>
          <div className={`p-2 rounded ${cdnLoaded ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            CDN Stylesheet Loaded: {cdnLoaded ? '✅ Yes' : '❌ No'}
          </div>
          <div className={`p-2 rounded ${localFontLoaded ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            Local Font Files Available: {localFontLoaded ? '✅ Yes' : '❌ No'}
          </div>
        </div>
      </div>

      {/* Test Icons */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Test Icons</h2>
        <div className="grid grid-cols-5 gap-4">
          {testIcons.map((icon, index) => (
            <div key={index} className="text-center p-4 border rounded-lg">
              <i className={`${icon.class} text-2xl block mb-2`}></i>
              <div className="text-sm font-medium">{icon.name}</div>
              <div className="text-xs text-gray-500">{icon.class}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Raw HTML Test */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Raw HTML Test</h2>
        <div className="p-4 bg-gray-50 rounded-lg">
          <p className="mb-2">Direct HTML icons (should show if font is loaded):</p>
          <div className="space-x-4">
            <i className="ri-home-line text-2xl"></i>
            <i className="ri-user-line text-2xl"></i>
            <i className="ri-settings-line text-2xl"></i>
            <i className="ri-search-line text-2xl"></i>
            <i className="ri-heart-line text-2xl"></i>
          </div>
        </div>
      </div>

      {/* CSS Test */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">CSS Font Family Test</h2>
        <div className="p-4 bg-gray-50 rounded-lg">
          <div 
            className="text-2xl"
            style={{ fontFamily: 'remixicon' }}
          >
            Should show RemixIcon font if loaded: 
          </div>
        </div>
      </div>

      {/* Network Test */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Network Test</h2>
        <div className="p-4 bg-gray-50 rounded-lg">
          <p className="mb-2">Check browser Network tab for:</p>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>CDN request to: https://cdn.jsdelivr.net/npm/remixicon@4.2.0/fonts/remixicon.css</li>
            <li>Font file requests (woff2, woff, ttf)</li>
            <li>Any 404 or CORS errors</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
