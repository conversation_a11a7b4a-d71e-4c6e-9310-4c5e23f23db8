@use "../public/assets/scss/tailwind/tailwind";

@import "../public/assets/css/styles.css";
@import "simplebar-react/dist/simplebar.min.css";

/* RemixIcon CSS with absolute paths */
@font-face {
  font-family: "remixicon";
  src: url('/assets/icon-fonts/RemixIcons/fonts/remixicon.eot?t=1708865856766'); /* IE9*/
  src: url('/assets/icon-fonts/RemixIcons/fonts/remixicon.eot?t=1708865856766#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url("/assets/icon-fonts/RemixIcons/fonts/remixicon.woff2?t=1708865856766") format("woff2"),
  url("/assets/icon-fonts/RemixIcons/fonts/remixicon.woff?t=1708865856766") format("woff"),
  url('/assets/icon-fonts/RemixIcons/fonts/remixicon.ttf?t=1708865856766') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('/assets/icon-fonts/RemixIcons/fonts/remixicon.svg?t=1708865856766#remixicon') format('svg'); /* iOS 4.1- */
  font-display: swap;
}

[class^="ri-"], [class*=" ri-"] {
  font-family: 'remixicon' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Common RemixIcon definitions */
.ri-home-line:before { content: "\ee2b"; }
.ri-user-line:before { content: "\f264"; }
.ri-settings-line:before { content: "\f0ee"; }
.ri-search-line:before { content: "\f0d1"; }
.ri-heart-line:before { content: "\ee0f"; }
.ri-arrow-down-s-line:before { content: "\ea4e"; }
.ri-clipboard-line:before { content: "\eb91"; }
.ri-check-line:before { content: "\eb7b"; }

/* Import Tabler Icons from CDN as fallback */
@import url("https://cdn.jsdelivr.net/npm/@tabler/icons@2.11.0/tabler-icons.min.css");

/* Custom animations moved to CSS modules in app/css/animations/ */

/* SignIn animations moved to CSS modules in app/css/animations/background-cycle.module.css */

/* Performance optimizations moved to CSS modules in app/css/utilities/performance.module.css */

/* Global performance optimizations that need to remain global */
* {
  font-display: swap;
}

img {
  height: auto;
  max-width: 100%;
}

table {
  table-layout: fixed;
}

/* Browser extension compatibility and hydration fixes */
/* Hide elements added by browser extensions that might cause hydration mismatches */
[bis_skin_checked],
[data-adblock-key],
[data-ublock],
[data-ghostery],
[data-avast],
[data-kaspersky],
[data-mcafee],
[data-norton],
[data-malwarebytes],
[data-extension-id] {
  /* Don't hide the elements, just ensure they don't affect layout */
  /* Browser extensions need these attributes for functionality */
}

/* Prevent browser extension injected styles from affecting our layout */
* {
  /* Ensure consistent box-sizing */
  box-sizing: border-box;
}

/* Suppress hydration warnings for elements that might be modified by extensions */
.hydration-safe {
  /* This class can be used on elements that are known to be modified by browser extensions */
}
